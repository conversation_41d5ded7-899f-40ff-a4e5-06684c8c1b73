import { type LessonPlanNode, type CreateNodeResponse } from './lessonPlanMapper';

/**
 * Mock API for testing lesson plan node creation
 * In production, replace this with actual API calls
 */
class MockLessonPlanAPI {
  private static nodeIdCounter = 1;
  private static createdNodes: CreateNodeResponse[] = [];

  /**
   * Mock API endpoint for creating lesson plan nodes
   */
  static async createNode(node: LessonPlanNode): Promise<CreateNodeResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100));

    // Create response with auto-generated ID
    const response: CreateNodeResponse = {
      id: this.nodeIdCounter++,
      lessonPlanId: node.lessonPlanId,
      parentId: node.parentId,
      title: node.title,
      content: node.content,
      type: node.type,
      orderIndex: node.orderIndex
    };

    // Store created node
    this.createdNodes.push(response);

    console.log('Mock API: Created node', response);
    return response;
  }

  /**
   * Get all created nodes (for testing purposes)
   */
  static getCreatedNodes(): CreateNodeResponse[] {
    return [...this.createdNodes];
  }

  /**
   * Clear all created nodes (for testing purposes)
   */
  static clearNodes(): void {
    this.createdNodes = [];
    this.nodeIdCounter = 1;
  }

  /**
   * Get nodes by lesson plan ID
   */
  static getNodesByLessonPlanId(lessonPlanId: number): CreateNodeResponse[] {
    return this.createdNodes.filter(node => node.lessonPlanId === lessonPlanId);
  }

  /**
   * Get child nodes by parent ID
   */
  static getChildNodes(parentId: number): CreateNodeResponse[] {
    return this.createdNodes.filter(node => node.parentId === parentId);
  }

  /**
   * Get root nodes (nodes with no parent)
   */
  static getRootNodes(lessonPlanId: number): CreateNodeResponse[] {
    return this.createdNodes.filter(
      node => node.lessonPlanId === lessonPlanId && node.parentId === null
    );
  }

  /**
   * Build hierarchical structure from flat nodes
   */
  static buildHierarchy(lessonPlanId: number): any[] {
    const allNodes = this.getNodesByLessonPlanId(lessonPlanId);
    const rootNodes = this.getRootNodes(lessonPlanId);

    const buildChildren = (parentId: number): any[] => {
      const children = this.getChildNodes(parentId);
      return children.map(child => ({
        ...child,
        children: buildChildren(child.id)
      }));
    };

    return rootNodes.map(root => ({
      ...root,
      children: buildChildren(root.id)
    }));
  }
}

/**
 * Setup mock fetch for lesson plan API
 * Call this in your app initialization to intercept API calls
 */
export function setupMockLessonPlanAPI() {
  // Store original fetch
  const originalFetch = global.fetch;

  // Override fetch for lesson plan API endpoints
  global.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
    const url = typeof input === 'string' ? input : input.toString();
    
    // Intercept lesson plan node creation
    if (url.includes('/api/lesson-plan-nodes') && init?.method === 'POST') {
      try {
        const body = JSON.parse(init.body as string) as LessonPlanNode;
        const result = await MockLessonPlanAPI.createNode(body);
        
        return new Response(JSON.stringify(result), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        return new Response(JSON.stringify({ error: 'Invalid request body' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    // Intercept lesson plan node retrieval
    if (url.includes('/api/lesson-plan-nodes') && init?.method === 'GET') {
      const urlObj = new URL(url, 'http://localhost');
      const lessonPlanId = urlObj.searchParams.get('lessonPlanId');
      
      if (lessonPlanId) {
        const nodes = MockLessonPlanAPI.buildHierarchy(parseInt(lessonPlanId));
        return new Response(JSON.stringify(nodes), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    // Fall back to original fetch for other requests
    return originalFetch(input, init);
  };
}

export { MockLessonPlanAPI };

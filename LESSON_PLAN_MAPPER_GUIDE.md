# Lesson Plan Mapper System

Hệ thống chuyển đổi template lesson plan thành các lesson plan nodes với cấu trúc phân cấp.

## 🎯 Mục đích

Sau khi tạo lesson plan thành công và nhận được `lessonPlanId`, hệ thống sẽ tự động:
1. Map template data thành các lesson plan nodes
2. Gọi API để tạo từng node với cấu trúc parent-child
3. Xử lý nested structure với parentId relationships

## 🏗️ Cấu trúc

### LessonPlanNode
```typescript
interface LessonPlanNode {
  lessonPlanId: number;
  parentId: number | null;
  title: string;
  content: string;
  type: "SECTION" | "SUBSECTION" | "LIST_ITEM" | "PARAGRAPH";
  orderIndex: number;
}
```

### API Response
```typescript
interface CreateNodeResponse {
  id: number;
  lessonPlanId: number;
  parentId: number | null;
  title: string;
  content: string;
  type: string;
  orderIndex: number;
}
```

## 🔄 Workflow

1. **Template Creation**: User tạo lesson plan template
2. **API Call**: Call `lessonPlan()` API và nhận `lessonPlanId`
3. **Auto Conversion**: Trong `onSuccess` callback:
   - Convert template sang format phù hợp
   - Tạo LessonPlanMapper instance
   - Gọi `convertTemplateToLessonPlan()`
4. **Recursive Processing**: 
   - Tạo step nodes trước (parentId = null)
   - Nhận response với node ID
   - Sử dụng node ID làm parentId cho keywords
   - Tiếp tục recursive cho children

## 📝 Cách sử dụng

### Trong Template Builder

```typescript
lessonPlan(payloadLessonplan, {
  onSuccess: async (res: any) => {
    const lessonPlanId = res?.data?.id;
    
    // Auto convert template to nodes
    const { LessonPlanMapper, convertLessonPlanTemplateToTemplate } = 
      await import('@/utils/lessonPlanMapper');
    
    const templateForMapper = convertLessonPlanTemplateToTemplate(template);
    const mapper = new LessonPlanMapper(lessonPlanId);
    
    await mapper.convertTemplateToLessonPlan(templateForMapper);
  }
});
```

### Standalone Usage

```typescript
import { LessonPlanMapper } from '@/utils/lessonPlanMapper';

const mapper = new LessonPlanMapper(lessonPlanId);
await mapper.convertTemplateToLessonPlan(template);
```

## 🧪 Testing

### Mock API
```typescript
import { setupMockLessonPlanAPI } from '@/utils/mockLessonPlanAPI';

// Setup mock API for testing
setupMockLessonPlanAPI();

// Use with mock API
const mapper = new LessonPlanMapper(lessonPlanId, '/api/lesson-plan-nodes', true);
```

### Demo Page
Truy cập `/lesson-plan-converter-demo` để xem demo với mock data.

## 📊 Data Flow

```
Template Steps
    ↓
Step Nodes (parentId: null)
    ↓ (API Response: stepNodeId)
Keyword Nodes (parentId: stepNodeId)
    ↓ (API Response: keywordNodeId)  
Children Nodes (parentId: keywordNodeId)
    ↓ (Recursive...)
Nested Children...
```

## 🔧 API Integration

### Real API
```typescript
POST /api/lesson-plan-nodes
{
  "lessonPlanId": 101,
  "parentId": null,
  "title": "Mục tiêu bài học",
  "content": "Học sinh hiểu được...",
  "type": "SECTION",
  "orderIndex": 1
}
```

### Response
```typescript
{
  "id": 1,
  "lessonPlanId": 101,
  "parentId": null,
  "title": "Mục tiêu bài học",
  "content": "Học sinh hiểu được...",
  "type": "SECTION",
  "orderIndex": 1
}
```

## 🎨 UI Components

### LessonPlanConverter
- Preview nodes trước khi convert
- Hiển thị progress và kết quả
- Support cả real API và mock API

### Integration trong Template Builder
- Tự động trigger sau khi save lesson plan
- Toast notifications cho user feedback
- Error handling và retry logic

## 🚀 Next Steps

1. **Real API Integration**: Thay thế mock API bằng real endpoints
2. **Error Handling**: Implement retry logic và rollback
3. **Progress Tracking**: Real-time progress cho large templates
4. **Validation**: Validate data trước khi gửi API
5. **Optimization**: Batch API calls nếu cần thiết

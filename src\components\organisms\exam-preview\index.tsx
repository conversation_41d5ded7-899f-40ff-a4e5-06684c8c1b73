"use client";

import React from "react";
import { Button } from "@/components/ui/Button";
import { Download, FileText, Eye } from "lucide-react";
import { Question } from "@/components/organisms/exam-question-item/types";
import { YesNoQuestion } from "@/components/organisms/yes-no-question-item/types";
import { ShortQuestion } from "@/components/organisms/short-question-item/types";
import { DowloadIcon } from "@/constants/icon";

interface ExamPreviewProps {
  questions: Question[];
  yesNoQuestions: YesNoQuestion[];
  shortQuestions: ShortQuestion[];
  examTitle?: string;
  examSubject?: string;
  examTime?: string;
  examDate?: string;
}

export default function ExamPreview({
  questions,
  yesNoQuestions,
  shortQuestions,
  examTitle = "Đề thi mẫu",
  examSubject = "Môn học",
  examTime = "90 phút",
  examDate = new Date().toLocaleDateString("vi-VN"),
}: ExamPreviewProps) {
  const handleExportDocx = async () => {
    try {
      // Import docx library dynamically
      const {
        Document,
        Packer,
        Paragraph,
        TextRun,
        HeadingLevel,
        AlignmentType,
      } = await import("docx");

      const doc = new Document({
        sections: [
          {
            properties: {},
            children: [
              // Header
              new Paragraph({
                children: [
                  new TextRun({
                    text: examTitle,
                    bold: true,
                    size: 32,
                  }),
                ],
                alignment: AlignmentType.CENTER,
                heading: HeadingLevel.TITLE,
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: `Môn: ${examSubject} | Thời gian: ${examTime} | Ngày: ${examDate}`,
                    size: 24,
                  }),
                ],
                alignment: AlignmentType.CENTER,
              }),
              new Paragraph({ text: "" }), // Empty line

              // Multiple Choice Questions
              ...(questions.length > 0
                ? [
                    new Paragraph({
                      children: [
                        new TextRun({
                          text: "PHẦN I: TRẮC NGHIỆM",
                          bold: true,
                          size: 28,
                        }),
                      ],
                      heading: HeadingLevel.HEADING_1,
                    }),
                    ...questions.flatMap((question, index) => [
                      new Paragraph({
                        children: [
                          new TextRun({
                            text: `Câu ${index + 1}: ${question.question}`,
                            bold: true,
                            size: 24,
                          }),
                        ],
                      }),
                      ...question?.options?.map(
                        (option, optIndex) =>
                          new Paragraph({
                            children: [
                              new TextRun({
                                text: `${String.fromCharCode(
                                  65 + optIndex
                                )}. ${option}`,
                                size: 22,
                              }),
                            ],
                          })
                      ),
                      new Paragraph({ text: "" }), // Empty line
                    ]),
                  ]
                : []),

              // Yes/No Questions
              ...(yesNoQuestions.length > 0
                ? [
                    new Paragraph({
                      children: [
                        new TextRun({
                          text: "PHẦN II: ĐÚNG/SAI",
                          bold: true,
                          size: 28,
                        }),
                      ],
                      heading: HeadingLevel.HEADING_1,
                    }),
                    ...yesNoQuestions.flatMap((question, index) => [
                      new Paragraph({
                        children: [
                          new TextRun({
                            text: `Câu ${questions.length + index + 1}: ${
                              question.question
                            }`,
                            bold: true,
                            size: 24,
                          }),
                        ],
                      }),
                      new Paragraph({
                        children: [
                          new TextRun({
                            text: `a) ${question.statements.a.text}`,
                            size: 22,
                          }),
                        ],
                      }),
                      new Paragraph({
                        children: [
                          new TextRun({
                            text: `b) ${question.statements.b.text}`,
                            size: 22,
                          }),
                        ],
                      }),
                      new Paragraph({
                        children: [
                          new TextRun({
                            text: `c) ${question.statements.c.text}`,
                            size: 22,
                          }),
                        ],
                      }),
                      new Paragraph({
                        children: [
                          new TextRun({
                            text: `d) ${question.statements.d.text}`,
                            size: 22,
                          }),
                        ],
                      }),
                      new Paragraph({ text: "" }), // Empty line
                    ]),
                  ]
                : []),

              // Short Answer Questions
              ...(shortQuestions.length > 0
                ? [
                    new Paragraph({
                      children: [
                        new TextRun({
                          text: "PHẦN III: TỰ LUẬN",
                          bold: true,
                          size: 28,
                        }),
                      ],
                      heading: HeadingLevel.HEADING_1,
                    }),
                    ...shortQuestions.flatMap((question, index) => [
                      new Paragraph({
                        children: [
                          new TextRun({
                            text: `Câu ${
                              questions.length +
                              yesNoQuestions.length +
                              index +
                              1
                            }: ${question.question}`,
                            bold: true,
                            size: 24,
                          }),
                        ],
                      }),
                      new Paragraph({
                        children: [
                          new TextRun({
                            text: "................................................................................................................................................................................................",
                            size: 22,
                          }),
                        ],
                      }),
                      new Paragraph({
                        children: [
                          new TextRun({
                            text: "................................................................................................................................................................................................",
                            size: 22,
                          }),
                        ],
                      }),
                      new Paragraph({ text: "" }), // Empty line
                    ]),
                  ]
                : []),
            ],
          },
        ],
      });

      // Generate and download
      const blob = await Packer.toBlob(doc);
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `${examTitle.replace(
        /\s+/g,
        "_"
      )}_${new Date().getTime()}.docx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error exporting to DOCX:", error);
      alert("Có lỗi xảy ra khi xuất file DOCX. Vui lòng thử lại.");
    }
  };

  const totalQuestions =
    questions.length + yesNoQuestions.length + shortQuestions.length;

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Eye className="w-5 h-5 text-blue-600" />
            <h2 className="text-lg font-calsans text-gray-800">Xem trước</h2>
          </div>
          <Button onClick={handleExportDocx}>
            {DowloadIcon}
            <span>Xuất DOCX</span>
          </Button>
        </div>

        <div className="text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <span>Tổng số câu: {totalQuestions}</span>
            <span>•</span>
            <span>Trắc nghiệm: {questions.length}</span>
            <span>•</span>
            <span>Đúng/Sai: {yesNoQuestions.length}</span>
            <span>•</span>
            <span>Tự luận: {shortQuestions.length}</span>
          </div>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 overflow-y-auto p-6 bg-gray-50">
        <div className="max-w-4xl mx-auto bg-white shadow-sm rounded-lg p-8">
          {/* Exam Header */}
          <div className="text-center mb-8 border-b pb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {examTitle}
            </h1>
            <div className="text-gray-600 space-y-1">
              <p>Môn: {examSubject}</p>
              <p>Thời gian làm bài: {examTime}</p>
              <p>Ngày thi: {examDate}</p>
            </div>
          </div>

          {/* Multiple Choice Section */}
          {questions.length > 0 && (
            <div className="mb-8">
              <h2 className="text-xl font-bold text-gray-900 mb-4">
                PHẦN I: TRẮC NGHIỆM
              </h2>
              <div className="space-y-4">
                {questions.map((question, index) => (
                  <div
                    key={question.id}
                    className="border-l-4 border-blue-500 pl-4"
                  >
                    <p className="font-medium text-gray-900 mb-2">
                      Câu {index + 1}: {question.question}
                    </p>
                    {question.illustrationImage && (
                      <div className="mb-3">
                        <img
                          src={question.illustrationImage}
                          alt="Hình minh họa"
                          className="max-w-xs max-h-48 rounded border"
                        />
                      </div>
                    )}
                    <div className="grid grid-cols-1 gap-1 ml-4">
                      {question.options.map((option, optIndex) => (
                        <p key={optIndex} className="text-gray-700">
                          {String.fromCharCode(65 + optIndex)}. {option}
                        </p>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Yes/No Section */}
          {yesNoQuestions.length > 0 && (
            <div className="mb-8">
              <h2 className="text-xl font-bold text-gray-900 mb-4">
                PHẦN II: ĐÚNG/SAI
              </h2>
              <div className="space-y-4">
                {yesNoQuestions.map((question, index) => (
                  <div
                    key={question.id}
                    className="border-l-4 border-green-500 pl-4"
                  >
                    <p className="font-medium text-gray-900 mb-2">
                      Câu {questions.length + index + 1}: {question.question}
                    </p>
                    <div className="grid grid-cols-1 gap-1 ml-4">
                      <p className="text-gray-700">
                        a) {question.statements.a.text}
                      </p>
                      <p className="text-gray-700">
                        b) {question.statements.b.text}
                      </p>
                      <p className="text-gray-700">
                        c) {question.statements.c.text}
                      </p>
                      <p className="text-gray-700">
                        d) {question.statements.d.text}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Short Answer Section */}
          {shortQuestions.length > 0 && (
            <div className="mb-8">
              <h2 className="text-xl font-bold text-gray-900 mb-4">
                PHẦN III: TỰ LUẬN
              </h2>
              <div className="space-y-6">
                {shortQuestions.map((question, index) => (
                  <div
                    key={question.id}
                    className="border-l-4 border-purple-500 pl-4"
                  >
                    <p className="font-medium text-gray-900 mb-3">
                      Câu {questions.length + yesNoQuestions.length + index + 1}
                      : {question.question}
                    </p>
                    <div className="space-y-2">
                      <div className="border-b border-dotted border-gray-400 h-6"></div>
                      <div className="border-b border-dotted border-gray-400 h-6"></div>
                      <div className="border-b border-dotted border-gray-400 h-6"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Empty State */}
          {totalQuestions === 0 && (
            <div className="text-center py-12">
              <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 text-lg">Chưa có câu hỏi nào</p>
              <p className="text-gray-400 text-sm">
                Thêm câu hỏi để xem trước đề thi
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

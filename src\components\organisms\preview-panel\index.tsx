"use client";

import React from "react";
import { CanvasElement } from "@/components/templates/canva-layout";
import { Download, Share2 } from "lucide-react";
import { Button } from "@/components/ui/Button";
import { DowloadIcon } from "@/constants/icon";

const deviceSize = {
  width: 1440,
  height: 900,
  scale: 1,
};

interface PreviewPanelProps {
  elements: CanvasElement[];
}

export default function PreviewPanel({ elements }: PreviewPanelProps) {
  const renderPreviewElement = (element: CanvasElement, scale: number) => {
    const style = {
      left: element.position.x * scale,
      top: element.position.y * scale,
      width: element.size.width * scale,
      height: element.size.height * scale,
      position: "absolute" as const,
    };

    const renderContent = () => {
      switch (element.type) {
        case "image":
          return (
            <div className="w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg overflow-hidden">
              <img
                src={element.content}
                alt="Preview element"
                className="w-full h-full object-contain"
                onError={(e) => {
                  e.currentTarget.style.display = "none";
                  e.currentTarget.nextElementSibling?.classList.remove(
                    "hidden"
                  );
                }}
              />
              <div className="w-full h-full hidden items-center justify-center">
                <span className="text-blue-600 text-xs">Image</span>
              </div>
            </div>
          );
        case "text":
          return (
            <div
              className="w-full h-full flex items-center justify-center p-1 text-center"
              style={element.style}
            >
              {element.content}
            </div>
          );
        case "shape":
          if (element.content === "rectangle") {
            return (
              <div className="w-full h-full bg-purple-200 border border-purple-400 rounded" />
            );
          } else if (element.content === "circle") {
            return (
              <div className="w-full h-full bg-purple-200 border border-purple-400 rounded-full" />
            );
          } else if (element.content === "triangle") {
            return (
              <div className="w-full h-full flex items-center justify-center">
                <div
                  className="w-0 h-0"
                  style={{
                    borderLeft: "12px solid transparent",
                    borderRight: "12px solid transparent",
                    borderBottom: "24px solid #c084fc",
                  }}
                />
              </div>
            );
          }
          break;
        default:
          return null;
      }
    };

    return (
      <div key={element.id} style={style}>
        {renderContent()}
      </div>
    );
  };

  const scale = deviceSize.scale;

  return (
    <div className="h-full flex flex-col font-questrial">
      {/* Header */}
      <div className="p-4 flex justify-between items-center">
        <h2 className="text-lg font-calsans text-gray-800">Xem trước</h2>
        {/* Actions */}
        <div className="space-x-2">
          <Button variant={"outline"}>
            <Share2 className="w-4 h-4" />
            <span>Chia sẻ</span>
          </Button>
          <Button>
            {DowloadIcon}
            <span>Tải về</span>
          </Button>
        </div>
      </div>

      {/* Preview Area */}
      <div className="flex-1 p-1.5  overflow-auto">
        <div className="flex items-center justify-center w-full h-full">
          <div
            className="overflow-hidden relative"
            style={{
              width: deviceSize.width * scale,
              height: deviceSize.height * scale,
            }}
          >
            <div className="relative w-full h-full overflow-hidden">
              {elements.length > 0 ? (
                elements.map((element) => renderPreviewElement(element, scale))
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  <div className="text-center">
                    <div className="text-sm font-medium mb-1">No elements</div>
                    <div className="text-xs">Add elements to see preview</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Element List */}
      {/* {elements.length > 0 && (
        <div className="border-t border-gray-200">
          <div className="p-4">
            <h3 className="text-sm font-medium text-gray-800 mb-2">
              Elements ({elements.length})
            </h3>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {elements.map((element, index) => (
                <div
                  key={element.id}
                  className="flex items-center justify-between text-xs text-gray-600 py-1"
                >
                  <span>
                    {element.type} {index + 1}
                  </span>
                  <span className="text-gray-400">
                    {element.position.x}, {element.position.y}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )} */}
    </div>
  );
}

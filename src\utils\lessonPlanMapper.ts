interface LessonPlanNode {
  lessonPlanId: number;
  parentId: number | null;
  title: string;
  content: string;
  type: "SECTION" | "SUBSECTION" | "LIST_ITEM" | "PARAGRAPH";
  orderIndex: number;
}

interface TemplateKeyword {
  id: string;
  title: string;
  content: string;
  order: number;
  nodeType?: "SECTION" | "SUBSECTION" | "LIST_ITEM" | "PARAGRAPH";
  children?: TemplateKeyword[];
}

interface TemplateStep {
  id: string;
  title: string;
  description?: string;
  isRequired: boolean;
  order: number;
  keywords: TemplateKeyword[];
  stepType: string;
}

interface Template {
  id: string;
  name: string;
  description: string;
  steps: TemplateStep[];
}

interface CreateNodeResponse {
  id: number;
  lessonPlanId: number;
  parentId: number | null;
  title: string;
  content: string;
  type: string;
  orderIndex: number;
}

class LessonPlanMapper {
  private lessonPlanId: number;
  private apiEndpoint: string;
  private useMockAPI: boolean;

  constructor(
    lessonPlanId: number,
    apiEndpoint: string = '/api/lesson-plan-nodes',
    useMockAPI: boolean = false
  ) {
    this.lessonPlanId = lessonPlanId;
    this.apiEndpoint = apiEndpoint;
    this.useMockAPI = useMockAPI;
  }

  /**
   * Convert template to lesson plan nodes and send to API
   */
  async convertTemplateToLessonPlan(template: Template): Promise<void> {
    try {
      // Process each step in the template
      for (const step of template.steps) {
        await this.processStep(step, null);
      }
      console.log('Template conversion completed successfully');
    } catch (error) {
      console.error('Error converting template to lesson plan:', error);
      throw error;
    }
  }

  /**
   * Process a template step and create corresponding nodes
   */
  private async processStep(step: TemplateStep, parentId: number | null): Promise<void> {
    // Create the main step node
    const stepNode: LessonPlanNode = {
      lessonPlanId: this.lessonPlanId,
      parentId: parentId,
      title: step.title,
      content: step.description || "",
      type: "SECTION",
      orderIndex: step.order
    };

    // Send step node to API and get response with ID
    const stepResponse = await this.createNode(stepNode);
    const stepNodeId = stepResponse.id;

    // Process all keywords under this step
    for (const keyword of step.keywords) {
      await this.processKeyword(keyword, stepNodeId);
    }
  }

  /**
   * Process a keyword and its children recursively
   */
  private async processKeyword(keyword: TemplateKeyword, parentId: number): Promise<void> {
    // Create the keyword node
    const keywordNode: LessonPlanNode = {
      lessonPlanId: this.lessonPlanId,
      parentId: parentId,
      title: keyword.title,
      content: keyword.content,
      type: keyword.nodeType || "LIST_ITEM",
      orderIndex: keyword.order
    };

    // Send keyword node to API and get response with ID
    const keywordResponse = await this.createNode(keywordNode);
    const keywordNodeId = keywordResponse.id;

    // Process children if they exist
    if (keyword.children && keyword.children.length > 0) {
      for (const child of keyword.children) {
        await this.processKeyword(child, keywordNodeId);
      }
    }
  }

  /**
   * Send node to API and return response
   */
  private async createNode(node: LessonPlanNode): Promise<CreateNodeResponse> {
    try {
      // Use mock API if enabled
      if (this.useMockAPI) {
        const { MockLessonPlanAPI } = await import('./mockLessonPlanAPI');
        return await MockLessonPlanAPI.createNode(node);
      }

      // Use real API
      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(node)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: CreateNodeResponse = await response.json();
      console.log(`Created node: ${result.title} with ID: ${result.id}`);
      return result;
    } catch (error) {
      console.error('Error creating node:', error);
      throw error;
    }
  }

  /**
   * Utility method to convert template data structure to flat array for preview
   */
  static previewNodes(template: Template, lessonPlanId: number): LessonPlanNode[] {
    const nodes: LessonPlanNode[] = [];
    let orderIndex = 0;

    const processStepForPreview = (step: TemplateStep, parentId: number | null) => {
      // Add step node
      nodes.push({
        lessonPlanId,
        parentId,
        title: step.title,
        content: step.description || "",
        type: "SECTION",
        orderIndex: orderIndex++
      });

      const stepIndex = nodes.length - 1;
      const stepId = stepIndex; // Mock ID for preview

      // Process keywords
      step.keywords.forEach(keyword => {
        processKeywordForPreview(keyword, stepId);
      });
    };

    const processKeywordForPreview = (keyword: TemplateKeyword, parentId: number) => {
      // Add keyword node
      nodes.push({
        lessonPlanId,
        parentId,
        title: keyword.title,
        content: keyword.content,
        type: keyword.nodeType || "LIST_ITEM",
        orderIndex: orderIndex++
      });

      const keywordIndex = nodes.length - 1;
      const keywordId = keywordIndex; // Mock ID for preview

      // Process children
      if (keyword.children) {
        keyword.children.forEach(child => {
          processKeywordForPreview(child, keywordId);
        });
      }
    };

    template.steps.forEach(step => {
      processStepForPreview(step, null);
    });

    return nodes;
  }
}

/**
 * Convert LessonPlanTemplate to Template format for mapper
 */
function convertLessonPlanTemplateToTemplate(lessonPlanTemplate: any): Template {
  return {
    id: lessonPlanTemplate.id,
    name: lessonPlanTemplate.name,
    description: lessonPlanTemplate.description,
    steps: lessonPlanTemplate.steps.map((step: any) => ({
      id: step.id,
      title: step.title,
      description: step.description || "",
      isRequired: step.isRequired,
      order: step.order,
      keywords: step.keywords || [],
      stepType: step.stepType
    }))
  };
}

export {
  LessonPlanMapper,
  convertLessonPlanTemplateToTemplate,
  type LessonPlanNode,
  type Template,
  type CreateNodeResponse
};

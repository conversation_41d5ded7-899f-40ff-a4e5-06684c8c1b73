'use client';

import React, { useState } from 'react';
import { CanvasElement } from '@/components/organisms/CanvaLayout';
import { Eye, Download, Share2, Smartphone, Tablet, Monitor } from 'lucide-react';

interface PreviewPanelProps {
  elements: CanvasElement[];
}

type DeviceType = 'desktop' | 'tablet' | 'mobile';

export default function PreviewPanel({ elements }: PreviewPanelProps) {
  const [selectedDevice, setSelectedDevice] = useState<DeviceType>('desktop');
  const [isFullPreview, setIsFullPreview] = useState(false);

  const deviceSizes = {
    desktop: { width: '100%', height: '100%', scale: 1 },
    tablet: { width: '768px', height: '1024px', scale: 0.3 },
    mobile: { width: '375px', height: '667px', scale: 0.4 }
  };

  const renderPreviewElement = (element: CanvasElement, scale: number = 1) => {
    const style = {
      left: element.position.x * scale,
      top: element.position.y * scale,
      width: element.size.width * scale,
      height: element.size.height * scale,
    };

    const renderContent = () => {
      switch (element.type) {
        case 'image':
          return (
            <div className="w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg overflow-hidden">
              <img
                src={element.content}
                alt="Preview element"
                className="w-full h-full object-contain"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling?.classList.remove('hidden');
                }}
              />
              <div className="w-full h-full hidden items-center justify-center">
                <span className="text-blue-600 text-xs">Image</span>
              </div>
            </div>
          );
        case 'text':
          return (
            <div 
              className="w-full h-full flex items-center justify-center p-1 text-center"
              style={{
                ...element.style,
                fontSize: element.style?.fontSize ? `${parseInt(element.style.fontSize) * scale}px` : `${14 * scale}px`
              }}
            >
              {element.content}
            </div>
          );
        case 'shape':
          if (element.content === 'rectangle') {
            return <div className="w-full h-full bg-purple-200 border border-purple-400 rounded" />;
          } else if (element.content === 'circle') {
            return <div className="w-full h-full bg-purple-200 border border-purple-400 rounded-full" />;
          } else if (element.content === 'triangle') {
            return (
              <div className="w-full h-full flex items-center justify-center">
                <div 
                  className="w-0 h-0"
                  style={{
                    borderLeft: `${12 * scale}px solid transparent`,
                    borderRight: `${12 * scale}px solid transparent`,
                    borderBottom: `${24 * scale}px solid #c084fc`
                  }}
                />
              </div>
            );
          }
          break;
        default:
          return null;
      }
    };

    return (
      <div
        key={element.id}
        className="absolute"
        style={style}
      >
        {renderContent()}
      </div>
    );
  };

  const devices = [
    { id: 'desktop', label: 'Desktop', icon: Monitor },
    { id: 'tablet', label: 'Tablet', icon: Tablet },
    { id: 'mobile', label: 'Mobile', icon: Smartphone },
  ] as const;

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-gray-800">Preview</h2>
          <button
            onClick={() => setIsFullPreview(!isFullPreview)}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            title="Full Preview"
          >
            <Eye className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        {/* Device Selector */}
        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
          {devices.map((device) => {
            const Icon = device.icon;
            return (
              <button
                key={device.id}
                onClick={() => setSelectedDevice(device.id)}
                className={`
                  flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors
                  ${selectedDevice === device.id 
                    ? 'bg-white text-blue-600 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-800'
                  }
                `}
              >
                <Icon className="w-4 h-4" />
                <span className="hidden sm:inline">{device.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Preview Area */}
      <div className="flex-1 p-4 bg-gray-50 overflow-auto">
        <div className="w-full h-full flex items-center justify-center">
          <div 
            className="bg-white shadow-lg rounded-lg overflow-hidden relative"
            style={{
              width: deviceSizes[selectedDevice].width,
              height: selectedDevice === 'desktop' ? '400px' : deviceSizes[selectedDevice].height,
              transform: selectedDevice !== 'desktop' ? `scale(${deviceSizes[selectedDevice].scale})` : undefined,
              transformOrigin: 'center center'
            }}
          >
            {/* Preview Canvas */}
            <div className="w-full h-full relative overflow-hidden">
              {elements.length > 0 ? (
                elements.map((element) => 
                  renderPreviewElement(element, deviceSizes[selectedDevice].scale)
                )
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  <div className="text-center">
                    <div className="text-sm font-medium mb-1">No elements</div>
                    <div className="text-xs">Add elements to see preview</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="p-4 border-t border-gray-200 space-y-2">
        <button className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          <Download className="w-4 h-4" />
          <span>Export</span>
        </button>
        <button className="w-full flex items-center justify-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
          <Share2 className="w-4 h-4" />
          <span>Share</span>
        </button>
      </div>

      {/* Element List */}
      {elements.length > 0 && (
        <div className="border-t border-gray-200">
          <div className="p-4">
            <h3 className="text-sm font-medium text-gray-800 mb-2">Elements ({elements.length})</h3>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {elements.map((element, index) => (
                <div key={element.id} className="flex items-center justify-between text-xs text-gray-600 py-1">
                  <span>{element.type} {index + 1}</span>
                  <span className="text-gray-400">
                    {element.position.x}, {element.position.y}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

"use client";

import React, { useEffect } from 'react';
import LessonPlanConverterExample from '@/components/organisms/lesson-plan-converter/example';
import { setupMockLessonPlanAPI } from '@/utils/mockLessonPlanAPI';

export default function LessonPlanConverterDemoPage() {
  // Setup mock API on component mount
  useEffect(() => {
    setupMockLessonPlanAPI();
  }, []);

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Lesson Plan Converter Demo</h1>
      <LessonPlanConverterExample />
    </div>
  );
}

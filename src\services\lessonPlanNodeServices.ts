import { createMutationHook, createQ<PERSON><PERSON><PERSON>ook } from "@/hooks/react-query";
import { API_ENDPOINTS } from "@/constants/apiEndpoints";

export const useCreateLessonPlanNodeService = createMutationHook(
  "lesson-plan-node",
  API_ENDPOINTS.LESSON_NODES
);

export const useLessonPlanNodeTreeService = (id: string) =>
  createQueryHook("lesson-plan-node-tree", API_ENDPOINTS.LESSON_NODES_TREE("21"));

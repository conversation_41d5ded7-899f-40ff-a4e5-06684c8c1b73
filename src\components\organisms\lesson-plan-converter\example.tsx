"use client";

import React from 'react';
import LessonPlanConverter from './index';

// Import template data
const templateData = {
  id: "template-1751473835162",
  name: "Template <PERSON><PERSON><PERSON>",
  description: "<PERSON><PERSON> tả template mới",
  version: "1.0",
  isDefault: true,
  createdBy: "system",
  createdAt: "2025-07-02T16:30:34.129Z",
  updatedAt: "2025-07-02T16:30:34.129Z",
  metadata: {
    subject: "Hóa học",
    educationLevel: "THPT",
    framework: "GDPT 2018",
  },
  steps: [
    {
      id: "2d1b9ef2-ea1c-4b21-a8b4-9dac9ab2f4e3",
      title: "Thông tin chung",
      description: "Thông tin cơ bản về bài giảng",
      isRequired: true,
      order: 0,
      keywords: [
        {
          id: "679f7318-4724-4436-ab79-290760503640",
          title: "Tên GV",
          content: "",
          order: 0,
          nodeType: "LIST_ITEM" as const,
        },
        {
          id: "690067e9-ae15-4576-9357-6225c423eb00",
          title: "Tên Tr<PERSON>ờng",
          content: "",
          order: 1,
          nodeType: "LIST_ITEM" as const,
        },
        {
          id: "3912b399-bf05-446a-9214-89f2bff5922c",
          title: "Bài",
          content: "",
          order: 2,
          nodeType: "LIST_ITEM" as const,
        },
      ],
      stepType: "general_info",
    },
    {
      id: "a71b81ac-2db5-483e-bf15-5a6f7b5d6f77",
      title: "I. Mục tiêu",
      description: "Mục tiêu kiến thức, năng lực và phẩm chất",
      isRequired: true,
      order: 1,
      keywords: [
        {
          id: "24643eee-2942-4511-be4a-60c51f16ced5",
          title: "1. Kiến thức",
          content: "",
          order: 0,
          children: [
            {
              id: "8b3e3358-f36e-483a-b129-321dac9dbd3b",
              title: "Nhận biết và trình bày được khái niệm…",
              content: "",
              order: 0,
              nodeType: "PARAGRAPH" as const,
            },
            {
              id: "8bdafb15-00a4-4d60-8ea4-0ef8dd52a338",
              title: "Hiểu được bản chất của hiện tượng/sự vật…",
              content: "",
              order: 1,
              nodeType: "PARAGRAPH" as const,
            },
          ],
          nodeType: "SECTION" as const,
        },
        {
          id: "2ad481cb-87c0-471b-9d8c-27a12a55b4f1",
          title: "2. Năng lực",
          content: "",
          order: 1,
          children: [
            {
              id: "0895e719-3228-4294-acc7-c1b761d808d5",
              title: "2.1. Năng lực chung",
              content: "",
              order: 0,
              children: [
                {
                  id: "f981192c-6c63-475a-96c5-a198972bea25",
                  title: "Năng lực tự chủ và tự học: ...",
                  content: "",
                  order: 0,
                  nodeType: "LIST_ITEM" as const,
                },
                {
                  id: "b1e6438d-bcb4-42a6-a4fa-e8c5109c01ea",
                  title: "Năng lực giao tiếp và hợp tác: ...",
                  content: "",
                  order: 1,
                  nodeType: "LIST_ITEM" as const,
                },
              ],
              nodeType: "LIST_ITEM" as const,
            },
          ],
          nodeType: "LIST_ITEM" as const,
        },
      ],
      stepType: "objectives",
    },
  ],
};

export default function LessonPlanConverterExample() {
  const handleConversionComplete = () => {
    console.log('Conversion completed!');
    // Redirect or refresh data
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Lesson Plan Converter Demo</h1>
      
      <LessonPlanConverter
        template={templateData}
        lessonPlanId={101}
        onConversionComplete={handleConversionComplete}
      />
    </div>
  );
}

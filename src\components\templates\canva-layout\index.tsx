'use client';

import React, { useState } from 'react';
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent } from '@dnd-kit/core';
import AssetsPanel from '@/components/organisms/assets-panel';
import CanvasArea from '@/components/organisms/canvas-area';
import PreviewPanel from '@/components/organisms/preview-panel';

export interface CanvasElement {
  id: string;
  type: 'image' | 'text' | 'shape';
  content: string;
  position: { x: number; y: number };
  size: { width: number; height: number };
  style?: Record<string, any>;
}

export default function CanvaLayout() {
  const [canvasElements, setCanvasElements] = useState<CanvasElement[]>([]);
  const [activeId, setActiveId] = useState<string | null>(null);

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && over.id === 'canvas-drop-zone') {
      // Get the asset data from the dragged item
      const assetData = active.data.current;

      if (assetData && !assetData.isCanvasElement) {
        // This is a new element being dragged from assets panel
        const newElement: CanvasElement = {
          id: `element-${Date.now()}`,
          type: assetData.type,
          content: assetData.content,
          position: { x: Math.random() * 200 + 50, y: Math.random() * 200 + 50 }, // Random position
          size: {
            width: assetData.type === 'text' ? 150 : 200,
            height: assetData.type === 'text' ? 50 : 150
          },
          style: assetData.style || {}
        };

        setCanvasElements(prev => [...prev, newElement]);
      }
    }

    setActiveId(null);
  };

  const updateElement = (id: string, updates: Partial<CanvasElement>) => {
    setCanvasElements(prev => 
      prev.map(element => 
        element.id === id ? { ...element, ...updates } : element
      )
    );
  };

  const deleteElement = (id: string) => {
    setCanvasElements(prev => prev.filter(element => element.id !== id));
  };

  return (
    <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
      <div className="grid grid-cols-6 h-full">
        {/* Assets Panel - 1 column */}
        <div className="col-span-1 bg-white border-r border-gray-200 ">
          <AssetsPanel />
        </div>

        {/* Canvas Area - 3 columns */}
        <div className="col-span-3 flex flex-col min-w-0">
          <div className="h-16 flex items-center px-4">
            <h1 className="text-xl font-calsans text-gray-800">Tạo bài kiểm tra</h1>
            <div className="ml-auto flex space-x-2">
              <button className="sm:hidden p-2 hover:bg-gray-100 rounded-lg">
                Assets
              </button>
              <button className="lg:hidden p-2 hover:bg-gray-100 rounded-lg">
                Preview
              </button>
            </div>
          </div>
          <div className="flex-1 p-2 sm:p-4">
            <CanvasArea
              elements={canvasElements}
              onUpdateElement={updateElement}
              onDeleteElement={deleteElement}
            />
          </div>
        </div>

        {/* Preview Panel - 2 columns */}
        <div className="col-span-2 bg-white border-l border-gray-200 ">
          <PreviewPanel elements={canvasElements} />
        </div>
      </div>

      {/* Drag Overlay */}
      <DragOverlay>
        {activeId ? (
          <div className="bg-blue-100 border-2 border-blue-300 rounded-lg p-2 opacity-80">
            Dragging...
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
}

'use client';

import React, { useState } from 'react';
import { useDraggable } from '@dnd-kit/core';
import { Image, Type, Square, Circle, Triangle } from 'lucide-react';

interface AssetItem {
  id: string;
  type: 'image' | 'text' | 'shape';
  content: string;
  preview: string;
  style?: Record<string, any>;
}

interface DraggableAssetProps {
  asset: AssetItem;
}

function DraggableAsset({ asset }: DraggableAssetProps) {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: asset.id,
    data: asset
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={`
        p-3 border border-gray-200 rounded-lg cursor-grab hover:border-blue-300 
        hover:shadow-md transition-all duration-200 bg-white
        ${isDragging ? 'opacity-50' : ''}
      `}
    >
      <div className="flex flex-col items-center space-y-2">
        {asset.type === 'image' && (
          <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center overflow-hidden">
            <img
              src={asset.content}
              alt={asset.preview}
              className="w-full h-full object-contain"
              onError={(e) => {
                // Fallback to icon if image fails to load
                e.currentTarget.style.display = 'none';
                e.currentTarget.nextElementSibling?.classList.remove('hidden');
              }}
            />
            <Image className="w-8 h-8 text-blue-600 hidden" />
          </div>
        )}
        {asset.type === 'text' && (
          <div className="w-16 h-16 bg-gradient-to-br from-green-100 to-green-200 rounded-lg flex items-center justify-center">
            <Type className="w-8 h-8 text-green-600" />
          </div>
        )}
        {asset.type === 'shape' && (
          <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg flex items-center justify-center">
            {asset.content === 'rectangle' && <Square className="w-8 h-8 text-purple-600" />}
            {asset.content === 'circle' && <Circle className="w-8 h-8 text-purple-600" />}
            {asset.content === 'triangle' && <Triangle className="w-8 h-8 text-purple-600" />}
          </div>
        )}
        <span className="text-sm text-gray-600 text-center">{asset.preview}</span>
      </div>
    </div>
  );
}

export default function AssetsPanel() {
  const [activeTab, setActiveTab] = useState<'images' | 'text' | 'shapes'>('images');

  const sampleAssets: Record<string, AssetItem[]> = {
    images: [
      { id: 'img-1', type: 'image', content: '/images/illustration/bookRequestCard.svg', preview: 'Book Request' },
      { id: 'img-2', type: 'image', content: '/images/illustration/folders.svg', preview: 'Folders' },
      { id: 'img-3', type: 'image', content: '/images/illustration/interaction.svg', preview: 'Interaction' },
      { id: 'img-4', type: 'image', content: '/images/illustration/packing.svg', preview: 'Packing' },
      { id: 'img-5', type: 'image', content: '/images/illustration/phone.svg', preview: 'Phone' },
      { id: 'img-6', type: 'image', content: '/images/logo/logoDark.svg', preview: 'Logo Dark' },
    ],
    text: [
      { id: 'text-1', type: 'text', content: 'Heading', preview: 'Heading', style: { fontSize: '24px', fontWeight: 'bold' } },
      { id: 'text-2', type: 'text', content: 'Subheading', preview: 'Subheading', style: { fontSize: '18px', fontWeight: '600' } },
      { id: 'text-3', type: 'text', content: 'Body Text', preview: 'Body Text', style: { fontSize: '14px' } },
      { id: 'text-4', type: 'text', content: 'Caption', preview: 'Caption', style: { fontSize: '12px', color: '#666' } },
    ],
    // shapes: [
    //   { id: 'shape-1', type: 'shape', content: 'rectangle', preview: 'Rectangle' },
    //   { id: 'shape-2', type: 'shape', content: 'circle', preview: 'Circle' },
    //   { id: 'shape-3', type: 'shape', content: 'triangle', preview: 'Triangle' },
    // ]
  };

  const tabs = [
    { id: 'images', label: 'Images', icon: Image },
    { id: 'text', label: 'Text', icon: Type },
    // { id: 'shapes', label: 'Shapes', icon: Square },
  ] as const;

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-calsans text-gray-800">Học liệu</h2>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                flex-1 flex items-center justify-center space-x-2 py-3 px-2 text-sm font-medium
                transition-colors duration-200
                ${activeTab === tab.id 
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50' 
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }
              `}
            >
              <Icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          );
        })}
      </div>

      {/* Assets Grid */}
      <div className="flex-1 p-2 sm:p-4 overflow-y-auto">
        <div className="grid grid-cols-2 lg:grid-cols-2 md:grid-cols-1 gap-2 sm:gap-3">
          {sampleAssets[activeTab].map((asset) => (
            <DraggableAsset key={asset.id} asset={asset} />
          ))}
        </div>
      </div>
    </div>
  );
}

"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { LessonPlanMapper, type LessonPlanNode, type Template } from '@/utils/lessonPlanMapper';

interface LessonPlanConverterProps {
  template: Template;
  lessonPlanId: number;
  onConversionComplete?: () => void;
  useMockAPI?: boolean;
}

export default function LessonPlanConverter({
  template,
  lessonPlanId,
  onConversionComplete,
  useMockAPI = true
}: LessonPlanConverterProps) {
  const [isConverting, setIsConverting] = useState(false);
  const [previewNodes, setPreviewNodes] = useState<LessonPlanNode[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [conversionResult, setConversionResult] = useState<any[]>([]);

  const handlePreview = () => {
    const nodes = LessonPlanMapper.previewNodes(template, lessonPlanId);
    setPreviewNodes(nodes);
    setShowPreview(true);
  };

  const handleConvert = async () => {
    setIsConverting(true);
    try {
      const mapper = new LessonPlanMapper(lessonPlanId, '/api/lesson-plan-nodes', useMockAPI);
      await mapper.convertTemplateToLessonPlan(template);

      // If using mock API, get the results
      if (useMockAPI) {
        const { MockLessonPlanAPI } = await import('@/utils/mockLessonPlanAPI');
        const result = MockLessonPlanAPI.buildHierarchy(lessonPlanId);
        setConversionResult(result);
      }

      alert('Template đã được chuyển đổi thành công!');
      if (onConversionComplete) {
        onConversionComplete();
      }
    } catch (error) {
      console.error('Conversion failed:', error);
      alert('Có lỗi xảy ra khi chuyển đổi template!');
    } finally {
      setIsConverting(false);
    }
  };

  const renderNodeType = (type: string) => {
    switch (type) {
      case 'SECTION':
        return <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">SECTION</span>;
      case 'LIST_ITEM':
        return <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">LIST_ITEM</span>;
      case 'PARAGRAPH':
        return <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded">PARAGRAPH</span>;
      default:
        return <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">{type}</span>;
    }
  };

  const getIndentLevel = (parentId: number | null, nodes: LessonPlanNode[]): number => {
    if (parentId === null) return 0;
    
    const parent = nodes.find((_, index) => index === parentId);
    if (!parent) return 1;
    
    return 1 + getIndentLevel(parent.parentId, nodes);
  };

  return (
    <div className="space-y-4">
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-semibold mb-4">Chuyển đổi Template thành Lesson Plan</h3>
        
        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-2">
            <strong>Template:</strong> {template.name}
          </p>
          <p className="text-sm text-gray-600 mb-2">
            <strong>Lesson Plan ID:</strong> {lessonPlanId}
          </p>
          <p className="text-sm text-gray-600">
            <strong>Số bước:</strong> {template.steps.length}
          </p>
        </div>

        <div className="flex space-x-3">
          <Button 
            onClick={handlePreview}
            variant="outline"
            disabled={isConverting}
          >
            Xem trước
          </Button>
          
          <Button 
            onClick={handleConvert}
            disabled={isConverting}
          >
            {isConverting ? 'Đang chuyển đổi...' : 'Chuyển đổi'}
          </Button>
        </div>
      </div>

      {showPreview && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h4 className="text-md font-semibold mb-4">Xem trước các Node sẽ được tạo</h4>
          
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {previewNodes.map((node, index) => {
              const indentLevel = getIndentLevel(node.parentId, previewNodes);
              
              return (
                <div 
                  key={index}
                  className="border rounded p-3"
                  style={{ marginLeft: `${indentLevel * 20}px` }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">#{node.orderIndex}</span>
                      {renderNodeType(node.type)}
                      {node.parentId !== null && (
                        <span className="text-xs text-gray-500">
                          Parent: #{node.parentId}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <h5 className="font-medium text-sm mb-1">{node.title}</h5>
                  {node.content && (
                    <p className="text-xs text-gray-600">{node.content}</p>
                  )}
                </div>
              );
            })}
          </div>
          
          <div className="mt-4 p-3 bg-gray-50 rounded">
            <p className="text-sm text-gray-600">
              <strong>Tổng số nodes sẽ được tạo:</strong> {previewNodes.length}
            </p>
          </div>
        </div>
      )}

      {conversionResult.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h4 className="text-md font-semibold mb-4">Kết quả chuyển đổi</h4>

          <div className="space-y-2 max-h-96 overflow-y-auto">
            {conversionResult.map((node, index) => (
              <div key={index} className="border rounded p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">ID: {node.id}</span>
                    {renderNodeType(node.type)}
                    {node.parentId !== null && (
                      <span className="text-xs text-gray-500">
                        Parent: {node.parentId}
                      </span>
                    )}
                  </div>
                </div>

                <h5 className="font-medium text-sm mb-1">{node.title}</h5>
                {node.content && (
                  <p className="text-xs text-gray-600">{node.content}</p>
                )}

                {node.children && node.children.length > 0 && (
                  <div className="mt-2 ml-4 border-l-2 border-gray-200 pl-3">
                    <p className="text-xs text-gray-500 mb-1">
                      Children ({node.children.length}):
                    </p>
                    {node.children.map((child: any, childIndex: number) => (
                      <div key={childIndex} className="text-xs text-gray-600 mb-1">
                        • {child.title} (ID: {child.id})
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="mt-4 p-3 bg-green-50 rounded">
            <p className="text-sm text-green-700">
              <strong>✅ Chuyển đổi thành công!</strong> Đã tạo {conversionResult.length} root nodes
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
